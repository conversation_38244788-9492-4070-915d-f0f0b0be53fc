# 优化后的分层架构设计

## 新的架构层次

经过优化，项目现在采用更清晰的分层架构，更好地体现了插拔性和框架适配能力：

```
┌─────────────────────────────────────────────────────────────────┐
│                 sxr-spring-boot-starter                         │
│                   Spring Boot 启动器                            │
│              (依赖 sxr-servlet-adapter)                        │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                  Web框架适配层                                   │
│  ┌─────────────────────┐    ┌─────────────────────┐             │
│  │  sxr-servlet-adapter │    │ sxr-webflux-adapter │             │
│  │   Servlet API适配    │    │  WebFlux API适配    │             │
│  │  (传统阻塞式Web)     │    │  (响应式Web)        │             │
│  └─────────────────────┘    └─────────────────────┘             │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    sxr-integration                              │
│                     业务整合层                                   │
│              (组合签名和文件处理功能)                             │
└─────────────────────────────────────────────────────────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────────────┐    ┌─────────────────┐    ┌─────────────────┐
│sxr-signature- │    │ sxr-file-handler │    │   扩展插件      │
│    core       │    │                 │    │                 │
│  签名核心模块  │    │  文件处理模块    │    │  (插件接口)     │
│  (独立零依赖)  │    │  (独立零依赖)    │    │                 │
└───────────────┘    └─────────────────┘    └─────────────────┘
```

## 模块详细说明

### 1. 核心业务层 (不变)

#### sxr-signature-core
- **职责**: 独立的URL签名生成和验证
- **特点**: 零第三方依赖，纯JDK8实现
- **核心类**: `SignatureService`, `HmacSignatureService`

#### sxr-file-handler  
- **职责**: 独立的文件下载和预览处理
- **特点**: 零第三方依赖，支持多种文件格式
- **核心类**: `FileHandler`, `DefaultFileHandler`

### 2. 业务整合层 (不变)

#### sxr-integration
- **职责**: 组合签名和文件处理，提供完整业务逻辑
- **依赖**: `sxr-signature-core` + `sxr-file-handler`
- **核心类**: `SecureFileService`, `DefaultSecureFileService`

### 3. Web框架适配层 (新增)

#### sxr-servlet-adapter
- **职责**: 将业务逻辑适配到Servlet API
- **依赖**: `sxr-integration`
- **特点**: 
  - 传统阻塞式Web处理
  - 兼容所有Servlet容器
  - 提供标准HTTP接口

**核心接口**:
```java
public interface ServletFileService {
    void handleSecureDownload(HttpServletRequest request, HttpServletResponse response);
    void handleSecurePreview(HttpServletRequest request, HttpServletResponse response);
    String generateSecureUrl(HttpServletRequest request);
    void healthCheck(HttpServletRequest request, HttpServletResponse response);
}
```

**核心类**:
- `ServletFileService` - 服务接口
- `DefaultServletFileService` - 默认实现
- `ServletRequestParser` - 请求解析工具
- `ServletResponseWriter` - 响应写入工具
- `SecureFileServlet` - Servlet适配器

#### sxr-webflux-adapter
- **职责**: 将业务逻辑适配到WebFlux API
- **依赖**: `sxr-integration`
- **特点**:
  - 响应式非阻塞处理
  - 高并发性能优异
  - 函数式编程风格

**核心接口**:
```java
public interface ReactiveFileService {
    Mono<Void> handleSecureDownload(ServerHttpRequest request, ServerHttpResponse response);
    Mono<Void> handleSecurePreview(ServerHttpRequest request, ServerHttpResponse response);
    Mono<String> generateSecureUrl(ServerHttpRequest request);
    Mono<Void> healthCheck(ServerHttpRequest request, ServerHttpResponse response);
}
```

**核心类**:
- `ReactiveFileService` - 响应式服务接口
- `DefaultReactiveFileService` - 默认实现
- `ReactiveRequestParser` - 响应式请求解析
- `ReactiveResponseWriter` - 响应式响应写入
- `SecureFileHandler` - WebFlux处理器

### 4. 应用启动层 (优化)

#### sxr-spring-boot-starter
- **职责**: Spring Boot快速启动和自动配置
- **依赖**: `sxr-servlet-adapter` (默认)
- **特点**: 
  - 开箱即用
  - 自动配置Bean
  - 支持外部化配置

## 架构优势

### 1. 更好的插拔性

#### Web框架无关
```java
// 可以选择不同的Web适配器
@ConditionalOnClass(HttpServlet.class)
public ServletFileService servletAdapter() { ... }

@ConditionalOnClass(ServerHttpRequest.class) 
public ReactiveFileService webfluxAdapter() { ... }
```

#### 业务逻辑复用
- 核心业务逻辑(`sxr-integration`)完全独立于Web框架
- 同一套业务逻辑可以同时支持Servlet和WebFlux
- 未来可以轻松添加其他框架适配器(如Vert.x、Netty等)

### 2. 清晰的职责分离

#### 各层职责明确
- **核心层**: 纯业务逻辑，无框架依赖
- **适配层**: 框架协议转换，无业务逻辑
- **启动层**: 配置和组装，选择具体适配器

#### 依赖方向清晰
```
启动层 → 适配层 → 业务层 → 核心层
```

### 3. 更强的扩展能力

#### 支持多种部署方式
```java
// 传统Servlet应用
@SpringBootApplication
@Import(ServletAdapterConfiguration.class)
public class ServletApp { }

// 响应式WebFlux应用  
@SpringBootApplication
@Import(WebFluxAdapterConfiguration.class)
public class ReactiveApp { }

// 混合部署
@SpringBootApplication
@Import({ServletAdapterConfiguration.class, WebFluxAdapterConfiguration.class})
public class HybridApp { }
```

#### 框架适配器可独立使用
```java
// 在非Spring环境中使用Servlet适配器
ServletFileService service = new DefaultServletFileService(secureFileService);
service.handleSecureDownload(request, response);

// 在非Spring环境中使用WebFlux适配器
ReactiveFileService service = new DefaultReactiveFileService(secureFileService);
service.handleSecureDownload(request, response).subscribe();
```

## 使用场景

### 1. 传统Web应用
- 使用`sxr-servlet-adapter`
- 适合大多数企业级应用
- 成熟稳定，易于调试

### 2. 高并发应用
- 使用`sxr-webflux-adapter`
- 适合高并发、低延迟场景
- 响应式编程，资源利用率高

### 3. 微服务架构
- 可以根据服务特点选择不同适配器
- 文件服务使用WebFlux(高并发)
- 管理服务使用Servlet(简单稳定)

### 4. 渐进式迁移
- 现有Servlet应用可以无缝集成
- 需要时可以逐步迁移到WebFlux
- 业务逻辑无需修改

## 配置示例

### Servlet模式配置
```yaml
sxr:
  file-signature:
    adapter: servlet  # 选择适配器
    servlet:
      async: true     # 启用异步Servlet
```

### WebFlux模式配置  
```yaml
sxr:
  file-signature:
    adapter: webflux  # 选择适配器
    webflux:
      buffer-size: 8192
```

### 混合模式配置
```yaml
sxr:
  file-signature:
    adapters: [servlet, webflux]  # 同时启用
    default: servlet              # 默认适配器
```

## 总结

这种分层架构设计实现了：

1. **业务与框架解耦**: 核心业务逻辑独立于Web框架
2. **多框架支持**: 同时支持Servlet和WebFlux
3. **插拔式设计**: 可以灵活选择和切换适配器
4. **向后兼容**: 现有代码无需修改
5. **未来扩展**: 易于添加新的框架适配器

这是一个真正体现**插件化架构**优势的设计！
