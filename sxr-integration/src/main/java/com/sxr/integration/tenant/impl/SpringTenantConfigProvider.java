package com.sxr.integration.tenant.impl;

import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.TenantConfigProvider;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于Spring配置的租户配置提供者
 * 兼容以往的配置方式，从Spring配置文件中读取租户信息
 * 支持从application.properties、application.yml等配置文件读取
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SpringTenantConfigProvider implements TenantConfigProvider {

    private final Map<String, TenantContext> tenantContexts = new ConcurrentHashMap<>();
    private volatile long lastRefreshTime = System.currentTimeMillis();

    /**
     * 构造函数，接受租户配置映射
     *
     * @param tenantConfigs 租户配置映射，key为租户ID，value为租户上下文
     */
    public SpringTenantConfigProvider(Map<String, TenantContext> tenantConfigs) {
        if (tenantConfigs != null) {
            this.tenantContexts.putAll(tenantConfigs);
        }

        // 如果没有配置，尝试从配置文件加载
        if (this.tenantContexts.isEmpty()) {
            loadFromConfigFiles();
        }

        // 如果仍然没有配置，加载默认配置
        if (this.tenantContexts.isEmpty()) {
            loadDefaultConfig();
        }
    }

    /**
     * 默认构造函数，从配置文件或使用默认配置
     */
    public SpringTenantConfigProvider() {
        // 尝试从配置文件加载
        loadFromConfigFiles();

        // 如果没有配置，加载默认配置
        if (this.tenantContexts.isEmpty()) {
            loadDefaultConfig();
        }
    }

    @Override
    public TenantContext getTenantContext(String tenantId) {
        return tenantContexts.get(tenantId);
    }

    @Override
    public boolean refresh() {
        try {
            // 重新从配置文件加载
            Map<String, TenantContext> oldContexts = new ConcurrentHashMap<>(tenantContexts);
            tenantContexts.clear();

            loadFromConfigFiles();

            // 如果没有加载到配置，恢复原有配置
            if (tenantContexts.isEmpty()) {
                tenantContexts.putAll(oldContexts);
                return false;
            }

            lastRefreshTime = System.currentTimeMillis();
            return true;
        } catch (Exception e) {
            System.err.println("Failed to refresh Spring tenant config: " + e.getMessage());
            return false;
        }
    }

    /**
     * 更新租户配置
     * 用于Spring容器重新注入配置时调用
     *
     * @param tenantConfigs 新的租户配置映射
     */
    public void updateTenantConfigs(Map<String, TenantContext> tenantConfigs) {
        tenantContexts.clear();
        if (tenantConfigs != null) {
            tenantContexts.putAll(tenantConfigs);
        }
    }

    /**
     * 添加单个租户配置
     *
     * @param tenantId 租户ID
     * @param context 租户上下文
     */
    public void addTenantConfig(String tenantId, TenantContext context) {
        tenantContexts.put(tenantId, context);
    }

    /**
     * 移除租户配置
     *
     * @param tenantId 租户ID
     * @return 被移除的租户上下文
     */
    public TenantContext removeTenantConfig(String tenantId) {
        return tenantContexts.remove(tenantId);
    }

    /**
     * 从配置文件加载租户配置
     * 支持从application.properties等Spring配置文件读取
     */
    private void loadFromConfigFiles() {
        // 尝试从多个可能的配置文件位置加载
        String[] configFiles = {
            "application.properties",
            "application-dev.properties",
            "application-prod.properties",
            "sxr.properties",
            "tenant.properties"
        };

        for (String configFile : configFiles) {
            if (loadFromPropertiesFile(configFile)) {
                System.out.println("Loaded tenant config from: " + configFile);
                return;
            }
        }

        // 尝试从系统属性加载
        loadFromSystemProperties();
    }

    /**
     * 从Properties文件加载配置
     */
    private boolean loadFromPropertiesFile(String fileName) {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream == null) {
                return false;
            }

            Properties properties = new Properties();
            properties.load(inputStream);

            return parsePropertiesConfig(properties);

        } catch (IOException e) {
            System.err.println("Failed to load config from " + fileName + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * 从系统属性加载配置
     */
    private void loadFromSystemProperties() {
        Properties systemProps = System.getProperties();
        parsePropertiesConfig(systemProps);
    }

    /**
     * 解析Properties配置
     * 支持格式：
     * sxr.tenant.{tenantId}.secretKey = xxx
     * sxr.tenant.{tenantId}.basePath = xxx
     * sxr.tenant.{tenantId}.name = xxx
     */
    private boolean parsePropertiesConfig(Properties properties) {
        Map<String, TenantContext> newContexts = new ConcurrentHashMap<>();
        boolean hasConfig = false;

        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith("sxr.tenant.")) {
                String value = properties.getProperty(key);

                // 解析租户ID
                String[] parts = key.split("\\.");
                if (parts.length >= 4) {
                    String tenantId = parts[2];
                    String property = parts[3];

                    TenantContext context = newContexts.computeIfAbsent(tenantId, k -> new TenantContext());
                    context.setTenantId(tenantId);

                    switch (property) {
                        case "secretKey":
                            context.setSecretKey(value);
                            hasConfig = true;
                            break;
                        case "basePath":
                            context.setBasePath(value);
                            break;
                        case "name":
                            context.setTenantName(value);
                            break;
                    }
                }
            }
        }

        if (hasConfig) {
            // 只保留有secretKey的租户
            for (Map.Entry<String, TenantContext> entry : newContexts.entrySet()) {
                if (entry.getValue().getSecretKey() != null) {
                    tenantContexts.put(entry.getKey(), entry.getValue());
                }
            }
        }

        return hasConfig;
    }

    /**
     * 获取最后刷新时间
     */
    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        // 默认租户1
        TenantContext tenant1Context = new TenantContext();
        tenant1Context.setTenantId("tenant1");
        tenant1Context.setTenantName("默认租户1");
        tenant1Context.setSecretKey("default-secret-key-change-in-production");
        tenant1Context.setBasePath("./files/tenant1");
        tenantContexts.put("tenant1", tenant1Context);

        // 默认租户2
        TenantContext tenant2Context = new TenantContext();
        tenant2Context.setTenantId("tenant2");
        tenant2Context.setTenantName("默认租户2");
        tenant2Context.setSecretKey("tenant2-secret-key");
        tenant2Context.setBasePath("./files/tenant2");
        tenantContexts.put("tenant2", tenant2Context);

        System.out.println("Loaded default tenant configuration");
    }
}
