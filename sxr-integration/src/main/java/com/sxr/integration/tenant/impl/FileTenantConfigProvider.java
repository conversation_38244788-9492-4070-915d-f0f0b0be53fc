package com.sxr.integration.tenant.impl;

import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.TenantConfigProvider;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于文件的租户配置提供者
 * 支持从properties文件或JSON文件读取配置
 * 支持文件变更监听和热更新
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FileTenantConfigProvider implements TenantConfigProvider {

    private final String configFilePath;
    private final Map<String, TenantContext> tenantContexts = new ConcurrentHashMap<>();
    private volatile long lastFileModified = 0;

    public FileTenantConfigProvider(String configFilePath) {
        this.configFilePath = configFilePath;
        loadConfig();
    }

    @Override
    public TenantContext getTenantContext(String tenantId) {
        // 检查文件是否有更新
        checkAndReloadIfNeeded();
        return tenantContexts.get(tenantId);
    }

    @Override
    public boolean refresh() {
        try {
            loadConfig();
            return true;
        } catch (Exception e) {
            System.err.println("Failed to refresh tenant config: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查文件是否有更新，如果有则重新加载
     */
    private void checkAndReloadIfNeeded() {
        File configFile = new File(configFilePath);
        if (configFile.exists() && configFile.lastModified() > lastFileModified) {
            refresh();
        }
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        File configFile = new File(configFilePath);
        if (!configFile.exists()) {
            System.out.println("Tenant config file not found: " + configFilePath + ", using default configuration");
            loadDefaultConfig();
            return;
        }

        try {
            if (configFilePath.endsWith(".json")) {
                loadJsonConfig(configFile);
            } else {
                loadPropertiesConfig(configFile);
            }

            lastFileModified = configFile.lastModified();

        } catch (IOException e) {
            System.err.println("Failed to load tenant config from " + configFilePath + ": " + e.getMessage());
            loadDefaultConfig();
        }
    }

    /**
     * 加载JSON格式配置
     */
    private void loadJsonConfig(File configFile) throws IOException {
        // 简单的JSON解析实现
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
        }

        parseJsonConfig(content.toString());
    }

    /**
     * 解析JSON配置
     */
    private void parseJsonConfig(String json) {
        Map<String, TenantContext> newContexts = new ConcurrentHashMap<>();

        // 简单的JSON解析 - 查找租户配置块
        // 格式: {"tenants": {"tenant1": {"secretKey": "key1", "basePath": "path1"}, ...}}

        String tenantsPattern = "\"tenants\"\\s*:\\s*\\{([^}]+)\\}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(tenantsPattern);
        java.util.regex.Matcher matcher = pattern.matcher(json);

        if (matcher.find()) {
            String tenantsContent = matcher.group(1);

            // 解析每个租户配置
            String tenantPattern = "\"([^\"]+)\"\\s*:\\s*\\{([^}]+)\\}";
            pattern = java.util.regex.Pattern.compile(tenantPattern);
            matcher = pattern.matcher(tenantsContent);

            while (matcher.find()) {
                String tenantId = matcher.group(1);
                String tenantConfig = matcher.group(2);

                TenantContext context = parseTenantConfig(tenantConfig);
                if (context != null) {
                    newContexts.put(tenantId, context);
                }
            }
        }

        // 更新内存中的配置
        tenantContexts.clear();
        tenantContexts.putAll(newContexts);
    }

    /**
     * 加载Properties格式配置
     */
    private void loadPropertiesConfig(File configFile) throws IOException {
        Map<String, TenantContext> newContexts = new ConcurrentHashMap<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    String key = parts[0].trim();
                    String value = parts[1].trim();

                    // 格式: tenant.{tenantId}.secretKey = value
                    if (key.startsWith("tenant.") && key.contains(".secretKey")) {
                        String tenantId = key.substring(7, key.lastIndexOf(".secretKey"));

                        TenantContext context = newContexts.computeIfAbsent(tenantId, k -> new TenantContext());
                        context.setSecretKey(value);
                    }
                    // 格式: tenant.{tenantId}.basePath = value
                    else if (key.startsWith("tenant.") && key.contains(".basePath")) {
                        String tenantId = key.substring(7, key.lastIndexOf(".basePath"));

                        TenantContext context = newContexts.computeIfAbsent(tenantId, k -> new TenantContext());
                        context.setBasePath(value);
                    }
                }
            }
        }

        // 更新内存中的配置
        tenantContexts.clear();
        tenantContexts.putAll(newContexts);
    }

    /**
     * 解析单个租户配置
     */
    private TenantContext parseTenantConfig(String config) {
        TenantContext context = new TenantContext();

        // 解析secretKey
        String secretKeyPattern = "\"secretKey\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(secretKeyPattern);
        java.util.regex.Matcher matcher = pattern.matcher(config);
        if (matcher.find()) {
            context.setSecretKey(matcher.group(1));
        }

        // 解析basePath
        String basePathPattern = "\"basePath\"\\s*:\\s*\"([^\"]+)\"";
        pattern = java.util.regex.Pattern.compile(basePathPattern);
        matcher = pattern.matcher(config);
        if (matcher.find()) {
            context.setBasePath(matcher.group(1));
        }

        return context.getSecretKey() != null ? context : null;
    }

    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        // 默认租户配置
        TenantContext defaultContext = new TenantContext();
        defaultContext.setSecretKey("default-secret-key-change-in-production");
        defaultContext.setBasePath("./files");
        tenantContexts.put("tenant1", defaultContext);

        TenantContext tenant2Context = new TenantContext();
        tenant2Context.setSecretKey("tenant2-secret-key");
        tenant2Context.setBasePath("./files/tenant2");
        tenantContexts.put("tenant2", tenant2Context);
    }
}
