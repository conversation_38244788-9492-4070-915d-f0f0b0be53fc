package com.sxr.integration.tenant;

import com.sxr.integration.TenantContext;

/**
 * 租户配置提供者接口
 * 支持多种配置源：文件、数据库、远程接口等
 * 支持动态配置和热更新
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TenantConfigProvider {

    /**
     * 根据租户ID获取租户上下文
     *
     * @param tenantId 租户ID
     * @return 租户上下文，如果不存在返回null
     */
    TenantContext getTenantContext(String tenantId);

    /**
     * 刷新配置
     * 用于热更新配置而不重启服务
     *
     * @return 是否刷新成功
     */
    boolean refresh();
}
