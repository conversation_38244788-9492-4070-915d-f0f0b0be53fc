package com.sxr.integration;

/**
 * 租户上下文
 * 存储当前请求的租户信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantContext {

    /** 租户ID */
    private String tenantId;

    /** 租户名称 */
    private String tenantName;

    /** 租户密钥 */
    private String secretKey;

    /** 文件基础路径 */
    private String basePath;

    /** 租户配置 */
    private TenantConfig config;

    public TenantContext() {
    }

    public TenantContext(String tenantId) {
        this.tenantId = tenantId;
    }

    // Getter and Setter methods
    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    public TenantConfig getConfig() {
        return config;
    }

    public void setConfig(TenantConfig config) {
        this.config = config;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        TenantContext that = (TenantContext) obj;

        if (tenantId != null ? !tenantId.equals(that.tenantId) : that.tenantId != null) return false;
        if (tenantName != null ? !tenantName.equals(that.tenantName) : that.tenantName != null) return false;
        if (secretKey != null ? !secretKey.equals(that.secretKey) : that.secretKey != null) return false;
        return basePath != null ? basePath.equals(that.basePath) : that.basePath == null;
    }

    @Override
    public int hashCode() {
        int result = tenantId != null ? tenantId.hashCode() : 0;
        result = 31 * result + (tenantName != null ? tenantName.hashCode() : 0);
        result = 31 * result + (secretKey != null ? secretKey.hashCode() : 0);
        result = 31 * result + (basePath != null ? basePath.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "TenantContext{" +
                "tenantId='" + tenantId + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", basePath='" + basePath + '\'' +
                ", secretKey='***'" +
                '}';
    }
}
