package com.sxr.integration;

import com.sxr.file.FileResponse;

/**
 * 安全文件服务接口
 * 整合签名验证和文件处理功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SecureFileService {
    
    /**
     * 处理安全文件下载请求
     * 先验证签名，再处理文件下载
     * 
     * @param request 安全文件请求
     * @return 文件响应结果
     */
    FileResponse handleSecureDownload(SecureFileRequest request);
    
    /**
     * 处理安全文件预览请求
     * 先验证签名，再处理文件预览
     * 
     * @param request 安全文件请求
     * @return 文件响应结果
     */
    FileResponse handleSecurePreview(SecureFileRequest request);
    
    /**
     * 生成安全访问URL
     * 
     * @param request 安全文件请求
     * @return 带签名的安全URL
     */
    String generateSecureUrl(SecureFileRequest request);
}
