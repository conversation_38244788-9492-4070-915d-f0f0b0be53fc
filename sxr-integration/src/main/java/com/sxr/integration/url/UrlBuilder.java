package com.sxr.integration.url;

import com.sxr.integration.SecureFileRequest;

/**
 * URL构建器接口
 * 用于构建安全文件访问URL
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UrlBuilder {
    
    /**
     * 构建预览URL
     *
     * @param request 文件请求
     * @param signature 签名
     * @return 预览URL
     */
    String buildPreviewUrl(SecureFileRequest request, String signature);
    
    /**
     * 构建下载URL
     *
     * @param request 文件请求
     * @param signature 签名
     * @return 下载URL
     */
    String buildDownloadUrl(SecureFileRequest request, String signature);
    
    /**
     * 构建通用URL（不指定预览或下载）
     * 可以通过路径来区分用途
     *
     * @param request 文件请求
     * @param signature 签名
     * @param isPreview 是否为预览模式
     * @return 安全URL
     */
    String buildSecureUrl(SecureFileRequest request, String signature, boolean isPreview);
}
