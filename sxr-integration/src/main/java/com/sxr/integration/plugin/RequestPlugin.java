package com.sxr.integration.plugin;

import com.sxr.integration.SecureFileRequest;
import com.sxr.integration.TenantContext;
import com.sxr.file.FileResponse;

/**
 * 请求插件接口
 * 提供请求处理的扩展点
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RequestPlugin {
    
    /**
     * 请求预处理
     * 在签名验证之前执行
     * 
     * @param request 安全文件请求
     * @param context 租户上下文
     * @return true表示继续处理，false表示中断处理
     */
    boolean preHandle(SecureFileRequest request, TenantContext context);
    
    /**
     * 请求后处理
     * 在文件处理完成后执行
     * 
     * @param request 安全文件请求
     * @param response 文件响应
     * @param context 租户上下文
     */
    void postHandle(SecureFileRequest request, FileResponse response, TenantContext context);
    
    /**
     * 获取插件名称
     * 
     * @return 插件名称
     */
    String getName();
    
    /**
     * 获取插件优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    int getOrder();
}
