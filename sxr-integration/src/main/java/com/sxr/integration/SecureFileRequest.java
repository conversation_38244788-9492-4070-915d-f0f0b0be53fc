package com.sxr.integration;

import java.util.Map;

/**
 * 安全文件请求参数
 * 整合签名和文件请求参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SecureFileRequest {
    
    /** 租户ID */
    private String tenantId;
    
    /** 文件路径 */
    private String filePath;
    
    /** 文件名 */
    private String fileName;
    
    /** 签名 */
    private String signature;
    
    /** 过期时间戳 */
    private Long expireTime;
    
    /** 访问次数限制 */
    private Integer accessLimit;
    
    /** 客户端IP */
    private String clientIp;
    
    /** User-Agent */
    private String userAgent;
    
    /** Range请求头 */
    private String rangeHeader;
    
    /** 是否为预览模式 */
    private boolean preview;
    
    /** 扩展参数 */
    private Map<String, String> extraParams;
    
    /** 请求头 */
    private Map<String, String> headers;
    
    public SecureFileRequest() {
    }
    
    public SecureFileRequest(String tenantId, String filePath) {
        this.tenantId = tenantId;
        this.filePath = filePath;
    }
    
    // Getter and Setter methods
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getSignature() {
        return signature;
    }
    
    public void setSignature(String signature) {
        this.signature = signature;
    }
    
    public Long getExpireTime() {
        return expireTime;
    }
    
    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }
    
    public Integer getAccessLimit() {
        return accessLimit;
    }
    
    public void setAccessLimit(Integer accessLimit) {
        this.accessLimit = accessLimit;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getRangeHeader() {
        return rangeHeader;
    }
    
    public void setRangeHeader(String rangeHeader) {
        this.rangeHeader = rangeHeader;
    }
    
    public boolean isPreview() {
        return preview;
    }
    
    public void setPreview(boolean preview) {
        this.preview = preview;
    }
    
    public Map<String, String> getExtraParams() {
        return extraParams;
    }
    
    public void setExtraParams(Map<String, String> extraParams) {
        this.extraParams = extraParams;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileName == null) {
            return null;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "SecureFileRequest{" +
                "tenantId='" + tenantId + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileName='" + fileName + '\'' +
                ", signature='***'" +
                ", expireTime=" + expireTime +
                ", accessLimit=" + accessLimit +
                ", clientIp='" + clientIp + '\'' +
                ", preview=" + preview +
                '}';
    }
}
