package com.sxr.integration.util;

/**
 * JSON工具类
 * 提供JSON相关的通用方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class JsonUtils {
    
    private JsonUtils() {
        // 工具类，禁止实例化
    }
    
    /**
     * JSON字符串转义
     * 使用JDK自带的字符串替换方法
     * 
     * @param str 需要转义的字符串
     * @return 转义后的字符串
     */
    public static String escapeJson(String str) {
        if (str == null) {
            return "";
        }
        
        // 使用JDK自带的字符串替换，按照JSON标准进行转义
        return str.replace("\\", "\\\\")    // 反斜杠必须最先处理
                  .replace("\"", "\\\"")    // 双引号
                  .replace("\n", "\\n")     // 换行符
                  .replace("\r", "\\r")     // 回车符
                  .replace("\t", "\\t")     // 制表符
                  .replace("\b", "\\b")     // 退格符
                  .replace("\f", "\\f");    // 换页符
    }
    
    /**
     * 构建简单的JSON响应
     * 
     * @param success 是否成功
     * @param url 成功时的URL
     * @param error 失败时的错误信息
     * @return JSON字符串
     */
    public static String buildResponse(boolean success, String url, String error) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"success\":").append(success);
        
        if (success && url != null) {
            json.append(",\"url\":\"").append(escapeJson(url)).append("\"");
        }
        
        if (!success && error != null) {
            json.append(",\"error\":\"").append(escapeJson(error)).append("\"");
        }
        
        json.append("}");
        return json.toString();
    }
    
    /**
     * 构建错误响应
     * 
     * @param error 错误信息
     * @return JSON字符串
     */
    public static String buildErrorResponse(String error) {
        return buildResponse(false, null, error);
    }
    
    /**
     * 构建成功响应
     * 
     * @param url 成功的URL
     * @return JSON字符串
     */
    public static String buildSuccessResponse(String url) {
        return buildResponse(true, url, null);
    }
}
