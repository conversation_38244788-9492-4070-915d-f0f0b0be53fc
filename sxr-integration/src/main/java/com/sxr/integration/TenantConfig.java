package com.sxr.integration;

/**
 * 租户配置
 * 存储租户的个性化配置信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantConfig {
    
    /** 文件存储基础路径 */
    private String basePath;
    
    /** 默认过期时间（秒） */
    private Long defaultExpireSeconds;
    
    /** 最大文件大小（字节） */
    private Long maxFileSize;
    
    /** 是否启用访问日志 */
    private boolean enableAccessLog;
    
    /** 是否启用限流 */
    private boolean enableRateLimit;
    
    /** 每秒最大请求数 */
    private Integer maxRequestsPerSecond;
    
    /** 允许的文件扩展名 */
    private String[] allowedExtensions;
    
    /** 禁止的文件扩展名 */
    private String[] forbiddenExtensions;
    
    public TenantConfig() {
        // 设置默认值
        this.defaultExpireSeconds = 3600L; // 1小时
        this.maxFileSize = 100 * 1024 * 1024L; // 100MB
        this.enableAccessLog = true;
        this.enableRateLimit = false;
        this.maxRequestsPerSecond = 100;
    }
    
    // Getter and Setter methods
    public String getBasePath() {
        return basePath;
    }
    
    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }
    
    public Long getDefaultExpireSeconds() {
        return defaultExpireSeconds;
    }
    
    public void setDefaultExpireSeconds(Long defaultExpireSeconds) {
        this.defaultExpireSeconds = defaultExpireSeconds;
    }
    
    public Long getMaxFileSize() {
        return maxFileSize;
    }
    
    public void setMaxFileSize(Long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }
    
    public boolean isEnableAccessLog() {
        return enableAccessLog;
    }
    
    public void setEnableAccessLog(boolean enableAccessLog) {
        this.enableAccessLog = enableAccessLog;
    }
    
    public boolean isEnableRateLimit() {
        return enableRateLimit;
    }
    
    public void setEnableRateLimit(boolean enableRateLimit) {
        this.enableRateLimit = enableRateLimit;
    }
    
    public Integer getMaxRequestsPerSecond() {
        return maxRequestsPerSecond;
    }
    
    public void setMaxRequestsPerSecond(Integer maxRequestsPerSecond) {
        this.maxRequestsPerSecond = maxRequestsPerSecond;
    }
    
    public String[] getAllowedExtensions() {
        return allowedExtensions;
    }
    
    public void setAllowedExtensions(String[] allowedExtensions) {
        this.allowedExtensions = allowedExtensions;
    }
    
    public String[] getForbiddenExtensions() {
        return forbiddenExtensions;
    }
    
    public void setForbiddenExtensions(String[] forbiddenExtensions) {
        this.forbiddenExtensions = forbiddenExtensions;
    }
    
    /**
     * 检查文件扩展名是否被允许
     */
    public boolean isExtensionAllowed(String extension) {
        if (extension == null) {
            return false;
        }
        
        String lowerExt = extension.toLowerCase();
        
        // 如果有禁止列表，先检查是否在禁止列表中
        if (forbiddenExtensions != null) {
            for (String forbidden : forbiddenExtensions) {
                if (lowerExt.equals(forbidden.toLowerCase())) {
                    return false;
                }
            }
        }
        
        // 如果有允许列表，检查是否在允许列表中
        if (allowedExtensions != null) {
            for (String allowed : allowedExtensions) {
                if (lowerExt.equals(allowed.toLowerCase())) {
                    return true;
                }
            }
            return false; // 有允许列表但不在其中
        }
        
        return true; // 没有限制列表，默认允许
    }
}
