<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sxr</groupId>
        <artifactId>sxr-file-signature</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sxr-integration</artifactId>
    <packaging>jar</packaging>

    <name>SXR Integration</name>
    <description>整合模块 - 组合签名和文件处理功能</description>

    <dependencies>
        <!-- 依赖签名核心模块 -->
        <dependency>
            <groupId>com.sxr</groupId>
            <artifactId>sxr-signature-core</artifactId>
        </dependency>
        
        <!-- 依赖文件处理模块 -->
        <dependency>
            <groupId>com.sxr</groupId>
            <artifactId>sxr-file-handler</artifactId>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
