package com.sxr.webflux;

import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import reactor.core.publisher.Mono;

/**
 * 响应式文件服务接口
 * 提供基于WebFlux的响应式文件处理能力
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ReactiveFileService {
    
    /**
     * 处理安全文件下载请求
     * 
     * @param request 响应式HTTP请求
     * @param response 响应式HTTP响应
     * @return 异步处理结果
     */
    Mono<Void> handleSecureDownload(ServerHttpRequest request, ServerHttpResponse response);
    
    /**
     * 处理安全文件预览请求
     * 
     * @param request 响应式HTTP请求
     * @param response 响应式HTTP响应
     * @return 异步处理结果
     */
    Mono<Void> handleSecurePreview(ServerHttpRequest request, ServerHttpResponse response);
    
    /**
     * 生成安全访问URL
     * 
     * @param request 响应式HTTP请求
     * @return 异步JSON响应
     */
    Mono<String> generateSecureUrl(ServerHttpRequest request);
    
    /**
     * 健康检查
     * 
     * @param request 响应式HTTP请求
     * @param response 响应式HTTP响应
     * @return 异步处理结果
     */
    Mono<Void> healthCheck(ServerHttpRequest request, ServerHttpResponse response);
}
