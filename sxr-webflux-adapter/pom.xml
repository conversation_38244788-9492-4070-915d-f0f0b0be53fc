<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sxr</groupId>
        <artifactId>sxr-file-signature</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sxr-webflux-adapter</artifactId>
    <packaging>jar</packaging>

    <name>SXR WebFlux Adapter</name>
    <description>WebFlux适配器 - 基于响应式编程的Web层适配</description>

    <dependencies>
        <!-- 依赖整合模块 -->
        <dependency>
            <groupId>com.sxr</groupId>
            <artifactId>sxr-integration</artifactId>
        </dependency>
        
        <!-- Spring WebFlux -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <!-- Reactor Core -->
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
