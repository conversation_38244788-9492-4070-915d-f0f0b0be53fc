<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sxr</groupId>
        <artifactId>sxr-file-signature</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sxr-signature-core</artifactId>
    <packaging>jar</packaging>

    <name>SXR Signature Core</name>
    <description>独立的URL签名生成和验证模块 - 零第三方依赖</description>

    <dependencies>
        <!-- 仅依赖JDK8标准库，无第三方依赖 -->
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
