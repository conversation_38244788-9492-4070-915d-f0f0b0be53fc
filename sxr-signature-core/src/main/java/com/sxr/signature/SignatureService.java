package com.sxr.signature;

import java.security.KeyPair;

/**
 * 签名服务接口
 * 提供URL签名生成和验证功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SignatureService {
    
    /**
     * 生成URL签名
     * 
     * @param request 签名请求参数
     * @return 生成的签名字符串
     */
    String generateSignature(SignRequest request);
    
    /**
     * 验证URL签名
     * 
     * @param url 原始URL
     * @param signature 待验证的签名
     * @return 验证结果，true表示签名有效
     */
    boolean verifySignature(String url, String signature);
    
    /**
     * 生成密钥对
     * 
     * @return RSA密钥对
     */
    KeyPair generateKeyPair();
    
    /**
     * 验证签名是否过期
     * 
     * @param request 签名请求参数
     * @return true表示已过期
     */
    boolean isExpired(SignRequest request);
}
