// 简单的签名测试
import java.util.HashMap;
import java.util.Map;

public class TestSignature {
    public static void main(String[] args) {
        // 模拟签名生成
        Map<String, String> params = new HashMap<>();
        params.put("tenantId", "tenant1");
        params.put("filePath", "images/test.jpg");
        params.put("expireTime", "1749391445000");
        
        // 按字典序排序
        StringBuilder sb = new StringBuilder();
        params.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                if (sb.length() > 0) sb.append("&");
                sb.append(entry.getKey()).append("=").append(entry.getValue());
            });
        
        System.out.println("签名字符串: " + sb.toString());
        // 应该是: expireTime=1749391445000&filePath=images/test.jpg&tenantId=tenant1
    }
}
