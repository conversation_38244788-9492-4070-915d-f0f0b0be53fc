# SXR文件签名系统使用指南

## 快速开始

### 1. 构建项目

```bash
mvn clean install
```

### 2. 启动应用

```bash
cd sxr-spring-boot-starter
mvn spring-boot:run
```

### 3. 测试健康检查

```bash
curl http://localhost:8080/sxr/health
```

## 使用示例

### 1. 生成安全URL

```bash
curl -X POST http://localhost:8080/sxr/generate-url \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "tenant1",
    "filePath": "images/test.jpg",
    "fileName": "test.jpg",
    "expireTime": 1749391445000,
    "preview": true
  }'
```

### 2. 访问安全文件

#### 预览文件
```bash
curl "http://localhost:8080/sxr/preview/tenant1/images/test.jpg?signature=xxx&expire=1703980800000"
```

#### 下载文件
```bash
curl "http://localhost:8080/sxr/download/tenant1/images/test.jpg?signature=xxx&expire=1703980800000"
```

## 配置说明

### 基础配置

```yaml
sxr:
  file-signature:
    enabled: true
    default-secret-key: "your-secret-key"
    base-path: "./files"
    default-expire-seconds: 3600
    max-file-size: 104857600
```

### 多租户配置

```yaml
sxr:
  file-signature:
    multi-tenant:
      enabled: true
      mode: prefix  # 或 domain
    tenants:
      tenant1:
        name: "租户1"
        secret-key: "tenant1-key"
        base-path: "./files/tenant1"
        allowed-extensions: ["jpg", "png", "pdf"]
```

## API接口

### 1. 文件预览
- **URL**: `/sxr/preview/{tenantId}/{filePath}`
- **方法**: GET
- **参数**:
  - `signature`: 签名（必需）
  - `expire`: 过期时间戳（可选）
  - `limit`: 访问次数限制（可选）

### 2. 文件下载
- **URL**: `/sxr/download/{tenantId}/{filePath}`
- **方法**: GET
- **参数**: 同预览接口

### 3. 生成安全URL
- **URL**: `/sxr/generate-url`
- **方法**: POST
- **请求体**: SecureFileRequest JSON

### 4. 健康检查
- **URL**: `/sxr/health`
- **方法**: GET

## 支持的文件格式

### 图片格式（支持预览）
- jpg, jpeg, png, gif, webp, bmp

### 视频格式（支持预览）
- mp4, webm

### 音频格式（支持预览）
- mp3, wav, ogg

### 文档格式
- pdf（支持预览）
- doc, docx, xls, xlsx, ppt, pptx（仅下载）

### 文本格式（支持预览）
- txt, html, css, js, json, xml

## 安全特性

1. **URL签名验证**: 基于HMAC-SHA256的签名机制
2. **时效控制**: 支持URL过期时间设置
3. **访问限制**: 支持访问次数限制
4. **多租户隔离**: 每个租户独立的密钥和存储空间
5. **文件类型控制**: 支持允许/禁止文件扩展名配置
6. **网络友好**: IP地址不参与签名计算，支持用户网络切换

## 扩展开发

### 1. 自定义签名算法

```java
@Component
public class CustomSignatureService implements SignatureService {
    // 实现自定义签名逻辑
}
```

### 2. 自定义文件处理器

```java
@Component
public class CustomFileHandler implements FileHandler {
    // 实现自定义文件处理逻辑
}
```

### 3. 自定义插件

```java
@Component
public class CustomPlugin implements RequestPlugin {
    @Override
    public boolean preHandle(SecureFileRequest request, TenantContext context) {
        // 请求预处理逻辑
        return true;
    }

    @Override
    public void postHandle(SecureFileRequest request, FileResponse response, TenantContext context) {
        // 请求后处理逻辑
    }
}
```

## 部署建议

### 1. 生产环境配置

```yaml
sxr:
  file-signature:
    default-secret-key: "${SXR_SECRET_KEY}"
    base-path: "/data/files"
    enable-access-log: true

logging:
  level:
    com.sxr: INFO
    root: WARN
```

### 2. 反向代理配置（Nginx）

```nginx
upstream sxr-backend {
    server 127.0.0.1:8080;
}

server {
    listen 80;
    server_name files.example.com;

    location /sxr/ {
        proxy_pass http://sxr-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 3. Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY sxr-spring-boot-starter/target/sxr-spring-boot-starter-1.0.0-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 性能优化

1. **文件缓存**: 配置适当的HTTP缓存头
2. **连接池**: 调整Tomcat连接池参数
3. **JVM参数**: 根据文件大小调整堆内存
4. **异步处理**: 大文件使用异步传输

## 监控指标

- 请求成功率
- 响应时间
- 文件传输速度
- 签名验证失败率
- 存储空间使用情况
