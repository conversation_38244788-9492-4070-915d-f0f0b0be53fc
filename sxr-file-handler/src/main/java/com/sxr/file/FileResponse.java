package com.sxr.file;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件响应结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FileResponse {
    
    /** HTTP状态码 */
    private int statusCode;
    
    /** 响应头 */
    private Map<String, String> headers;
    
    /** 文件输入流 */
    private InputStream inputStream;
    
    /** 文件大小 */
    private long contentLength;
    
    /** Content-Type */
    private String contentType;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 是否成功 */
    private boolean success;
    
    public FileResponse() {
        this.headers = new HashMap<>();
        this.success = false;
    }
    
    public static FileResponse success() {
        FileResponse response = new FileResponse();
        response.setSuccess(true);
        response.setStatusCode(200);
        return response;
    }
    
    public static FileResponse error(int statusCode, String errorMessage) {
        FileResponse response = new FileResponse();
        response.setSuccess(false);
        response.setStatusCode(statusCode);
        response.setErrorMessage(errorMessage);
        return response;
    }
    
    public static FileResponse notFound() {
        return error(404, "File not found");
    }
    
    public static FileResponse forbidden() {
        return error(403, "Access forbidden");
    }
    
    public static FileResponse internalError(String message) {
        return error(500, message);
    }
    
    // Getter and Setter methods
    public int getStatusCode() {
        return statusCode;
    }
    
    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public void addHeader(String name, String value) {
        this.headers.put(name, value);
    }
    
    public InputStream getInputStream() {
        return inputStream;
    }
    
    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }
    
    public long getContentLength() {
        return contentLength;
    }
    
    public void setContentLength(long contentLength) {
        this.contentLength = contentLength;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
        this.addHeader("Content-Type", contentType);
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    @Override
    public String toString() {
        return "FileResponse{" +
                "statusCode=" + statusCode +
                ", contentLength=" + contentLength +
                ", contentType='" + contentType + '\'' +
                ", success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
