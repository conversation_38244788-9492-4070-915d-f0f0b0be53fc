package com.sxr.file;

import java.util.Map;

/**
 * 文件请求参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FileRequest {
    
    /** 租户ID */
    private String tenantId;
    
    /** 文件路径 */
    private String filePath;
    
    /** 文件名 */
    private String fileName;
    
    /** 是否为预览模式 */
    private boolean preview;
    
    /** Range请求头 - 支持断点续传 */
    private String rangeHeader;
    
    /** 客户端IP */
    private String clientIp;
    
    /** User-Agent */
    private String userAgent;
    
    /** 扩展请求头 */
    private Map<String, String> headers;
    
    public FileRequest() {
    }
    
    public FileRequest(String tenantId, String filePath) {
        this.tenantId = tenantId;
        this.filePath = filePath;
    }
    
    // Getter and Setter methods
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public boolean isPreview() {
        return preview;
    }
    
    public void setPreview(boolean preview) {
        this.preview = preview;
    }
    
    public String getRangeHeader() {
        return rangeHeader;
    }
    
    public void setRangeHeader(String rangeHeader) {
        this.rangeHeader = rangeHeader;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileName == null) {
            return null;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "FileRequest{" +
                "tenantId='" + tenantId + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileName='" + fileName + '\'' +
                ", preview=" + preview +
                ", rangeHeader='" + rangeHeader + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", userAgent='" + userAgent + '\'' +
                '}';
    }
}
