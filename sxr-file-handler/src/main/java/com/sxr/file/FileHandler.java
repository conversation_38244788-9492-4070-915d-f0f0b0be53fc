package com.sxr.file;

/**
 * 文件处理器接口
 * 提供文件下载和预览功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FileHandler {
    
    /**
     * 处理文件下载请求
     * 
     * @param request 文件请求参数
     * @return 文件响应结果
     */
    FileResponse handleDownload(FileRequest request);
    
    /**
     * 处理文件预览请求
     * 
     * @param request 文件请求参数
     * @return 文件响应结果
     */
    FileResponse handlePreview(FileRequest request);
    
    /**
     * 检查是否支持指定文件格式
     * 
     * @param extension 文件扩展名
     * @return true表示支持该格式
     */
    boolean supportFormat(String extension);
    
    /**
     * 获取文件的Content-Type
     * 
     * @param extension 文件扩展名
     * @return MIME类型
     */
    String getContentType(String extension);
}
