package com.sxr.file.impl;

import com.sxr.file.FileHandler;
import com.sxr.file.FileRequest;
import com.sxr.file.FileResponse;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 默认文件处理器测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DefaultFileHandlerTest {
    
    private FileHandler fileHandler;
    
    @Before
    public void setUp() {
        fileHandler = new DefaultFileHandler();
    }
    
    @Test
    public void testSupportFormat() {
        // 测试支持的格式
        assertTrue("应该支持jpg格式", fileHandler.supportFormat("jpg"));
        assertTrue("应该支持png格式", fileHandler.supportFormat("png"));
        assertTrue("应该支持pdf格式", fileHandler.supportFormat("pdf"));
        assertTrue("应该支持mp4格式", fileHandler.supportFormat("mp4"));
        
        // 测试不支持的格式
        assertFalse("不应该支持未知格式", fileHandler.supportFormat("unknown"));
        assertFalse("null应该返回false", fileHandler.supportFormat(null));
    }
    
    @Test
    public void testGetContentType() {
        // 测试常见MIME类型
        assertEquals("image/jpeg", fileHandler.getContentType("jpg"));
        assertEquals("image/png", fileHandler.getContentType("png"));
        assertEquals("application/pdf", fileHandler.getContentType("pdf"));
        assertEquals("video/mp4", fileHandler.getContentType("mp4"));
        assertEquals("audio/mpeg", fileHandler.getContentType("mp3"));
        
        // 测试未知格式
        assertEquals("application/octet-stream", fileHandler.getContentType("unknown"));
        assertEquals("application/octet-stream", fileHandler.getContentType(null));
    }
    
    @Test
    public void testHandleDownloadWithInvalidRequest() {
        // 测试null请求
        FileResponse response = fileHandler.handleDownload(null);
        assertFalse("null请求应该返回失败", response.isSuccess());
        assertEquals(400, response.getStatusCode());
        
        // 测试空文件路径
        FileRequest request = new FileRequest();
        response = fileHandler.handleDownload(request);
        assertFalse("空文件路径应该返回失败", response.isSuccess());
        assertEquals(400, response.getStatusCode());
    }
    
    @Test
    public void testHandleDownloadWithNonExistentFile() {
        FileRequest request = new FileRequest();
        request.setTenantId("test-tenant");
        request.setFilePath("non-existent-file.jpg");
        request.setFileName("non-existent-file.jpg");
        
        FileResponse response = fileHandler.handleDownload(request);
        assertFalse("不存在的文件应该返回失败", response.isSuccess());
        assertEquals(404, response.getStatusCode());
    }
    
    @Test
    public void testHandlePreviewWithInvalidRequest() {
        // 测试null请求
        FileResponse response = fileHandler.handlePreview(null);
        assertFalse("null请求应该返回失败", response.isSuccess());
        assertEquals(400, response.getStatusCode());
    }
}
