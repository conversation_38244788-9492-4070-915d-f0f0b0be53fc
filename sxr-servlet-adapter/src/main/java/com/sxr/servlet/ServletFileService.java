package com.sxr.servlet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Servlet文件服务接口
 * 提供基于Servlet API的文件处理能力
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ServletFileService {
    
    /**
     * 处理安全文件下载请求
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void handleSecureDownload(HttpServletRequest request, HttpServletResponse response) throws IOException;
    
    /**
     * 处理安全文件预览请求
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void handleSecurePreview(HttpServletRequest request, HttpServletResponse response) throws IOException;
    
    /**
     * 生成安全访问URL
     * 
     * @param request HTTP请求
     * @return JSON格式的响应字符串
     * @throws IOException IO异常
     */
    String generateSecureUrl(HttpServletRequest request) throws IOException;
    
    /**
     * 健康检查
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void healthCheck(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
