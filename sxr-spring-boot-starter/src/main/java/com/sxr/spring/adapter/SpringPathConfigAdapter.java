package com.sxr.spring.adapter;

import com.sxr.servlet.config.PathConfig;
import com.sxr.spring.SxrFileSignatureProperties;

/**
 * Spring Boot配置属性到ServletRequestParser路径配置的适配器
 * 将Spring Boot的配置属性转换为ServletRequestParser可以使用的路径配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SpringPathConfigAdapter implements PathConfig {

    private final SxrFileSignatureProperties.PathConfig pathConfig;

    public SpringPathConfigAdapter(SxrFileSignatureProperties.PathConfig pathConfig) {
        this.pathConfig = pathConfig;
    }

    @Override
    public String getPreviewPath() {
        String preview = pathConfig.getPreview();
        // 确保路径以/结尾
        return preview.endsWith("/") ? preview : preview + "/";
    }

    @Override
    public String getDownloadPath() {
        String download = pathConfig.getDownload();
        // 确保路径以/结尾
        return download.endsWith("/") ? download : download + "/";
    }
}
