package com.sxr.spring.url;

import com.sxr.integration.SecureFileRequest;
import com.sxr.integration.url.UrlBuilder;
import com.sxr.spring.SxrFileSignatureProperties;

import java.util.Map;

/**
 * Spring配置化URL构建器
 * 使用Spring Boot配置属性来构建URL
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SpringConfigurableUrlBuilder implements UrlBuilder {
    
    private final SxrFileSignatureProperties.PathConfig pathConfig;
    
    public SpringConfigurableUrlBuilder(SxrFileSignatureProperties.PathConfig pathConfig) {
        this.pathConfig = pathConfig;
    }
    
    @Override
    public String buildPreviewUrl(SecureFileRequest request, String signature) {
        return buildSecureUrl(request, signature, true);
    }
    
    @Override
    public String buildDownloadUrl(SecureFileRequest request, String signature) {
        return buildSecureUrl(request, signature, false);
    }
    
    @Override
    public String buildSecureUrl(SecureFileRequest request, String signature, boolean isPreview) {
        StringBuilder urlBuilder = new StringBuilder();
        
        // 基础路径前缀
        urlBuilder.append(pathConfig.getBasePrefix());
        
        // 预览或下载路径
        if (isPreview) {
            urlBuilder.append(pathConfig.getPreview());
        } else {
            urlBuilder.append(pathConfig.getDownload());
        }
        
        // 确保路径以/结尾
        if (!urlBuilder.toString().endsWith("/")) {
            urlBuilder.append("/");
        }
        
        // 租户ID
        if (request.getTenantId() != null) {
            urlBuilder.append(request.getTenantId()).append("/");
        }
        
        // 文件路径
        urlBuilder.append(request.getFilePath());
        
        // 查询参数 - 对签名进行URL编码
        urlBuilder.append("?signature=").append(urlEncode(signature));
        
        if (request.getExpireTime() != null) {
            urlBuilder.append("&expire=").append(request.getExpireTime());
        }
        
        if (request.getAccessLimit() != null) {
            urlBuilder.append("&limit=").append(request.getAccessLimit());
        }
        
        // 添加extraParams到URL中
        if (request.getExtraParams() != null && !request.getExtraParams().isEmpty()) {
            for (Map.Entry<String, String> entry : request.getExtraParams().entrySet()) {
                urlBuilder.append("&").append(urlEncode(entry.getKey()))
                          .append("=").append(urlEncode(entry.getValue()));
            }
        }
        
        return urlBuilder.toString();
    }
    
    /**
     * URL编码 - 使用JDK自带的URLEncoder
     */
    private String urlEncode(String value) {
        if (value == null) {
            return "";
        }
        try {
            return java.net.URLEncoder.encode(value, "UTF-8");
        } catch (java.io.UnsupportedEncodingException e) {
            // UTF-8是标准编码，不会出现此异常
            throw new RuntimeException("UTF-8 encoding not supported", e);
        }
    }
}
