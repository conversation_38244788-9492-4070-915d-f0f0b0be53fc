package com.sxr.spring;

import com.sxr.file.FileHandler;
import com.sxr.file.impl.DefaultFileHandler;
import com.sxr.integration.SecureFileService;
import com.sxr.integration.impl.DefaultSecureFileService;
import com.sxr.integration.tenant.TenantConfigProvider;
import com.sxr.servlet.ServletFileService;
import com.sxr.servlet.impl.DefaultServletFileService;
import com.sxr.servlet.util.ServletRequestParser;
import com.sxr.signature.SignatureService;
import com.sxr.signature.impl.HmacSignatureService;
import com.sxr.spring.adapter.SpringPathConfigAdapter;
import com.sxr.spring.tenant.SpringBootTenantConfigProvider;
import com.sxr.spring.url.SpringConfigurableUrlBuilder;
import com.sxr.spring.web.SecureFileController;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SXR文件签名自动配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConditionalOnProperty(prefix = "sxr.file-signature", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(SxrFileSignatureProperties.class)
public class SxrFileSignatureAutoConfiguration {

    /**
     * 签名服务Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public SignatureService signatureService() {
        return new HmacSignatureService();
    }

    /**
     * 文件处理器Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public FileHandler fileHandler() {
        return new DefaultFileHandler();
    }

    /**
     * 租户配置提供者Bean
     * 使用Spring Boot配置属性提供者，支持从YAML配置文件读取租户信息
     */
    @Bean
    @ConditionalOnMissingBean
    public TenantConfigProvider tenantConfigProvider(SxrFileSignatureProperties properties) {
        return new SpringBootTenantConfigProvider(properties);
    }

    /**
     * 安全文件服务Bean
     * 使用配置化的URL构建器
     */
    @Bean
    @ConditionalOnMissingBean
    public SecureFileService secureFileService(SignatureService signatureService, FileHandler fileHandler, TenantConfigProvider tenantConfigProvider, SxrFileSignatureProperties properties) {
        // 创建配置化的URL构建器
        SpringConfigurableUrlBuilder urlBuilder = new SpringConfigurableUrlBuilder(properties.getPaths());
        return new DefaultSecureFileService(signatureService, fileHandler, tenantConfigProvider, urlBuilder);
    }

    /**
     * Servlet文件服务Bean
     * 提供Servlet适配层的文件服务，支持配置化路径
     */
    @Bean
    @ConditionalOnMissingBean
    public ServletFileService servletFileService(SecureFileService secureFileService, SxrFileSignatureProperties properties) {
        // 创建路径配置适配器
        SpringPathConfigAdapter pathConfig = new SpringPathConfigAdapter(properties.getPaths());
        // 创建ServletRequestParser
        ServletRequestParser requestParser = new ServletRequestParser(pathConfig);
        return new DefaultServletFileService(secureFileService, requestParser);
    }

    /**
     * 安全文件控制器Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public SecureFileController secureFileController(ServletFileService servletFileService) {
        return new SecureFileController(servletFileService);
    }
}
