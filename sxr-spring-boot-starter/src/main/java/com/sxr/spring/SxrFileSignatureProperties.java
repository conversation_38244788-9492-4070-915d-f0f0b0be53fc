package com.sxr.spring;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SXR文件签名配置属性
 * 用于映射application.yml中的配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "sxr.file-signature")
public class SxrFileSignatureProperties {

    /** 是否启用文件签名功能 */
    private boolean enabled = true;

    /** 默认密钥 */
    private String defaultSecretKey = "your-secret-key-here";

    /** 默认基础路径 */
    private String basePath = "./files";

    /** 默认过期时间（秒） */
    private int defaultExpireSeconds = 3600;

    /** 最大文件大小 */
    private long maxFileSize = 104857600L; // 100MB

    /** 是否启用访问日志 */
    private boolean enableAccessLog = true;

    /** 多租户配置 */
    private MultiTenant multiTenant = new MultiTenant();

    /** 租户配置映射 */
    private Map<String, TenantConfig> tenants = new HashMap<>();

    /** 路径配置 */
    private PathConfig paths = new PathConfig();

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDefaultSecretKey() {
        return defaultSecretKey;
    }

    public void setDefaultSecretKey(String defaultSecretKey) {
        this.defaultSecretKey = defaultSecretKey;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    public int getDefaultExpireSeconds() {
        return defaultExpireSeconds;
    }

    public void setDefaultExpireSeconds(int defaultExpireSeconds) {
        this.defaultExpireSeconds = defaultExpireSeconds;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public boolean isEnableAccessLog() {
        return enableAccessLog;
    }

    public void setEnableAccessLog(boolean enableAccessLog) {
        this.enableAccessLog = enableAccessLog;
    }

    public MultiTenant getMultiTenant() {
        return multiTenant;
    }

    public void setMultiTenant(MultiTenant multiTenant) {
        this.multiTenant = multiTenant;
    }

    public Map<String, TenantConfig> getTenants() {
        return tenants;
    }

    public void setTenants(Map<String, TenantConfig> tenants) {
        this.tenants = tenants;
    }

    public PathConfig getPaths() {
        return paths;
    }

    public void setPaths(PathConfig paths) {
        this.paths = paths;
    }

    /**
     * 多租户配置
     */
    public static class MultiTenant {
        /** 是否启用多租户 */
        private boolean enabled = false;

        /** 多租户模式：prefix 或 domain */
        private String mode = "prefix";

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }
    }

    /**
     * 租户配置
     */
    public static class TenantConfig {
        /** 租户名称 */
        private String name;

        /** 租户密钥 */
        private String secretKey;

        /** 租户基础路径 */
        private String basePath;

        /** 过期时间（秒） */
        private Integer expireSeconds;

        /** 最大文件大小 */
        private Long maxFileSize;

        /** 允许的文件扩展名 */
        private List<String> allowedExtensions;

        /** 禁止的文件扩展名 */
        private List<String> forbiddenExtensions;

        /** 是否启用限流 */
        private Boolean enableRateLimit;

        /** 每秒最大请求数 */
        private Integer maxRequestsPerSecond;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }

        public String getBasePath() {
            return basePath;
        }

        public void setBasePath(String basePath) {
            this.basePath = basePath;
        }

        public Integer getExpireSeconds() {
            return expireSeconds;
        }

        public void setExpireSeconds(Integer expireSeconds) {
            this.expireSeconds = expireSeconds;
        }

        public Long getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(Long maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public List<String> getAllowedExtensions() {
            return allowedExtensions;
        }

        public void setAllowedExtensions(List<String> allowedExtensions) {
            this.allowedExtensions = allowedExtensions;
        }

        public List<String> getForbiddenExtensions() {
            return forbiddenExtensions;
        }

        public void setForbiddenExtensions(List<String> forbiddenExtensions) {
            this.forbiddenExtensions = forbiddenExtensions;
        }

        public Boolean getEnableRateLimit() {
            return enableRateLimit;
        }

        public void setEnableRateLimit(Boolean enableRateLimit) {
            this.enableRateLimit = enableRateLimit;
        }

        public Integer getMaxRequestsPerSecond() {
            return maxRequestsPerSecond;
        }

        public void setMaxRequestsPerSecond(Integer maxRequestsPerSecond) {
            this.maxRequestsPerSecond = maxRequestsPerSecond;
        }
    }

    /**
     * 路径配置
     */
    public static class PathConfig {
        /** 基础路径前缀 */
        private String basePrefix = "";

        /** 预览路径 */
        private String preview = "/preview";

        /** 下载路径 */
        private String download = "/download";

        /** 生成URL路径 */
        private String generateUrl = "/generate-url";

        /** 健康检查路径 */
        private String health = "/health";

        /** 管理路径前缀 */
        private String adminPrefix = "/admin";

        public String getBasePrefix() {
            return basePrefix;
        }

        public void setBasePrefix(String basePrefix) {
            this.basePrefix = basePrefix;
        }

        public String getPreview() {
            return preview;
        }

        public void setPreview(String preview) {
            this.preview = preview;
        }

        public String getDownload() {
            return download;
        }

        public void setDownload(String download) {
            this.download = download;
        }

        public String getGenerateUrl() {
            return generateUrl;
        }

        public void setGenerateUrl(String generateUrl) {
            this.generateUrl = generateUrl;
        }

        public String getHealth() {
            return health;
        }

        public void setHealth(String health) {
            this.health = health;
        }

        public String getAdminPrefix() {
            return adminPrefix;
        }

        public void setAdminPrefix(String adminPrefix) {
            this.adminPrefix = adminPrefix;
        }

        /**
         * 获取完整的预览路径
         */
        public String getFullPreviewPath() {
            return basePrefix + preview;
        }

        /**
         * 获取完整的下载路径
         */
        public String getFullDownloadPath() {
            return basePrefix + download;
        }

        /**
         * 获取完整的生成URL路径
         */
        public String getFullGenerateUrlPath() {
            return basePrefix + generateUrl;
        }

        /**
         * 获取完整的健康检查路径
         */
        public String getFullHealthPath() {
            return basePrefix + health;
        }

        /**
         * 获取完整的管理路径前缀
         */
        public String getFullAdminPrefix() {
            return basePrefix + adminPrefix;
        }
    }
}
