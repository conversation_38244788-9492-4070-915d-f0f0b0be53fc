package com.sxr.spring.web;

import com.sxr.integration.tenant.TenantConfigProvider;
import com.sxr.integration.util.JsonUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租户配置管理控制器
 * 提供配置刷新等管理接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
public class TenantConfigController {
    
    private final TenantConfigProvider tenantConfigProvider;
    
    public TenantConfigController(TenantConfigProvider tenantConfigProvider) {
        this.tenantConfigProvider = tenantConfigProvider;
    }
    
    /**
     * 刷新租户配置
     * 
     * @return 刷新结果
     */
    @PostMapping("/refresh-config")
    public ResponseEntity<String> refreshConfig() {
        try {
            boolean success = tenantConfigProvider.refresh();
            if (success) {
                String successJson = JsonUtils.buildSuccessResponse("配置刷新成功");
                return ResponseEntity.ok()
                        .header("Content-Type", "application/json;charset=UTF-8")
                        .body(successJson);
            } else {
                String errorJson = JsonUtils.buildErrorResponse("配置刷新失败");
                return ResponseEntity.status(500)
                        .header("Content-Type", "application/json;charset=UTF-8")
                        .body(errorJson);
            }
            
        } catch (Exception e) {
            String errorJson = JsonUtils.buildErrorResponse("配置刷新异常: " + e.getMessage());
            return ResponseEntity.status(500)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(errorJson);
        }
    }
}
