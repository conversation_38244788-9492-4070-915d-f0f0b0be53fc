package com.sxr.spring.web;

import com.sxr.integration.util.JsonUtils;
import com.sxr.servlet.ServletFileService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全文件控制器
 * 提供文件下载和预览的HTTP接口
 * 基于Servlet适配器实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/sxr")
public class SecureFileController {

    private final ServletFileService servletFileService;

    public SecureFileController(ServletFileService servletFileService) {
        this.servletFileService = servletFileService;
    }

    /**
     * 安全文件下载
     */
    @GetMapping("/download/**")
    public void download(HttpServletRequest request, HttpServletResponse response) throws IOException {
        servletFileService.handleSecureDownload(request, response);
    }

    /**
     * 安全文件预览
     */
    @GetMapping("/preview/**")
    public void preview(HttpServletRequest request, HttpServletResponse response) throws IOException {
        System.out.println("=== Preview request received ===");
        System.out.println("Request URI: " + request.getRequestURI());
        System.out.println("Query String: " + request.getQueryString());
        System.out.println("Method: " + request.getMethod());

        try {
            servletFileService.handleSecurePreview(request, response);
            System.out.println("=== Preview request completed ===");
        } catch (Exception e) {
            System.err.println("=== Preview request failed ===");
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 生成安全URL
     */
    @PostMapping("/generate-url")
    public ResponseEntity<String> generateUrl(HttpServletRequest request) {
        try {
            String jsonResponse = servletFileService.generateSecureUrl(request);
            return ResponseEntity.ok()
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(jsonResponse);
        } catch (Exception e) {
            String errorJson = JsonUtils.buildErrorResponse(e.getMessage());
            return ResponseEntity.status(500)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(errorJson);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public void health(HttpServletRequest request, HttpServletResponse response) throws IOException {
        servletFileService.healthCheck(request, response);
    }

}
