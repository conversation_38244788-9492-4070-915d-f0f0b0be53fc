package com.sxr.spring.tenant;

import com.sxr.integration.TenantConfig;
import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.TenantConfigProvider;
import com.sxr.spring.SxrFileSignatureProperties;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于Spring Boot配置属性的租户配置提供者
 * 直接从application.yml等配置文件中读取租户配置
 * 支持热更新和动态配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SpringBootTenantConfigProvider implements TenantConfigProvider {

    private final SxrFileSignatureProperties properties;
    private final Map<String, TenantContext> tenantContextCache = new ConcurrentHashMap<>();
    private volatile long lastRefreshTime = System.currentTimeMillis();

    public SpringBootTenantConfigProvider(SxrFileSignatureProperties properties) {
        this.properties = properties;
        initializeTenantContexts();
    }

    @Override
    public TenantContext getTenantContext(String tenantId) {
        // 先从缓存获取
        TenantContext context = tenantContextCache.get(tenantId);
        if (context != null) {
            return context;
        }

        // 如果缓存中没有，尝试从配置中构建
        return buildTenantContext(tenantId);
    }

    @Override
    public boolean refresh() {
        try {
            // 清空缓存
            tenantContextCache.clear();

            // 重新初始化
            initializeTenantContexts();

            lastRefreshTime = System.currentTimeMillis();
            System.out.println("SpringBootTenantConfigProvider refreshed successfully");
            return true;
        } catch (Exception e) {
            System.err.println("Failed to refresh SpringBootTenantConfigProvider: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取最后刷新时间
     */
    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    /**
     * 获取所有租户ID
     */
    public String[] getAllTenantIds() {
        return properties.getTenants().keySet().toArray(new String[0]);
    }

    /**
     * 初始化租户上下文
     */
    private void initializeTenantContexts() {
        Map<String, SxrFileSignatureProperties.TenantConfig> tenantConfigs = properties.getTenants();

        if (tenantConfigs == null || tenantConfigs.isEmpty()) {
            System.out.println("No tenant configurations found, loading default configuration");
            loadDefaultTenantConfig();
            return;
        }

        for (Map.Entry<String, SxrFileSignatureProperties.TenantConfig> entry : tenantConfigs.entrySet()) {
            String tenantId = entry.getKey();
            SxrFileSignatureProperties.TenantConfig config = entry.getValue();

            TenantContext context = buildTenantContextFromConfig(tenantId, config);
            tenantContextCache.put(tenantId, context);
        }

        System.out.println("Loaded " + tenantContextCache.size() + " tenant configurations from Spring Boot properties");
    }

    /**
     * 从配置构建租户上下文
     */
    private TenantContext buildTenantContext(String tenantId) {
        SxrFileSignatureProperties.TenantConfig config = properties.getTenants().get(tenantId);
        if (config == null) {
            return null;
        }

        TenantContext context = buildTenantContextFromConfig(tenantId, config);
        tenantContextCache.put(tenantId, context);
        return context;
    }

    /**
     * 从Spring Boot配置构建租户上下文
     */
    private TenantContext buildTenantContextFromConfig(String tenantId, SxrFileSignatureProperties.TenantConfig config) {
        TenantContext context = new TenantContext();
        context.setTenantId(tenantId);

        // 设置租户名称
        context.setTenantName(config.getName() != null ? config.getName() : tenantId);

        // 设置密钥，如果没有配置则使用默认密钥
        context.setSecretKey(config.getSecretKey() != null ? config.getSecretKey() : properties.getDefaultSecretKey());

        // 设置基础路径，如果没有配置则使用默认路径 + 租户ID
        String basePath = config.getBasePath();
        if (basePath == null) {
            basePath = properties.getBasePath() + "/" + tenantId;
        }
        context.setBasePath(basePath);

        // 构建租户配置对象
        TenantConfig tenantConfig = new TenantConfig();

        // 设置过期时间
        tenantConfig.setDefaultExpireSeconds(config.getExpireSeconds() != null ?
            Long.valueOf(config.getExpireSeconds()) : Long.valueOf(properties.getDefaultExpireSeconds()));

        // 设置最大文件大小
        tenantConfig.setMaxFileSize(config.getMaxFileSize() != null ?
            config.getMaxFileSize() : properties.getMaxFileSize());

        // 设置允许的扩展名
        List<String> allowedExtensions = config.getAllowedExtensions();
        if (allowedExtensions != null && !allowedExtensions.isEmpty()) {
            tenantConfig.setAllowedExtensions(allowedExtensions.toArray(new String[0]));
        }

        // 设置禁止的扩展名
        List<String> forbiddenExtensions = config.getForbiddenExtensions();
        if (forbiddenExtensions != null && !forbiddenExtensions.isEmpty()) {
            tenantConfig.setForbiddenExtensions(forbiddenExtensions.toArray(new String[0]));
        }

        // 设置限流配置
        tenantConfig.setEnableRateLimit(config.getEnableRateLimit() != null ?
            config.getEnableRateLimit() : false);
        tenantConfig.setMaxRequestsPerSecond(config.getMaxRequestsPerSecond() != null ?
            config.getMaxRequestsPerSecond() : 100);

        context.setConfig(tenantConfig);

        return context;
    }

    /**
     * 加载默认租户配置
     */
    private void loadDefaultTenantConfig() {
        // 创建默认租户1
        TenantContext tenant1 = new TenantContext();
        tenant1.setTenantId("tenant1");
        tenant1.setTenantName("默认租户1");
        tenant1.setSecretKey(properties.getDefaultSecretKey());
        tenant1.setBasePath(properties.getBasePath() + "/tenant1");

        TenantConfig config1 = new TenantConfig();
        config1.setDefaultExpireSeconds(Long.valueOf(properties.getDefaultExpireSeconds()));
        config1.setMaxFileSize(properties.getMaxFileSize());
        config1.setEnableRateLimit(false);
        config1.setMaxRequestsPerSecond(100);
        tenant1.setConfig(config1);

        tenantContextCache.put("tenant1", tenant1);

        // 创建默认租户2
        TenantContext tenant2 = new TenantContext();
        tenant2.setTenantId("tenant2");
        tenant2.setTenantName("默认租户2");
        tenant2.setSecretKey("tenant2-secret-key");
        tenant2.setBasePath(properties.getBasePath() + "/tenant2");

        TenantConfig config2 = new TenantConfig();
        config2.setDefaultExpireSeconds(Long.valueOf(properties.getDefaultExpireSeconds()));
        config2.setMaxFileSize(properties.getMaxFileSize());
        config2.setEnableRateLimit(false);
        config2.setMaxRequestsPerSecond(100);
        tenant2.setConfig(config2);

        tenantContextCache.put("tenant2", tenant2);

        System.out.println("Loaded default tenant configurations");
    }
}
