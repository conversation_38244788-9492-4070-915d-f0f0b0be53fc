server:
  port: 8080

sxr:
  file-signature:
    enabled: true
    default-secret-key: "your-secret-key-here"
    base-path: "./files"
    default-expire-seconds: 3600
    max-file-size: 104857600  # 100MB
    enable-access-log: true
    
    # 多租户配置
    multi-tenant:
      enabled: true
      mode: prefix  # domain 或 prefix
    
    # 租户配置
    tenants:
      tenant1:
        name: "租户1"
        secret-key: "tenant1-secret-key"
        base-path: "./files/tenant1"
        expire-seconds: 7200
        max-file-size: 52428800  # 50MB
        allowed-extensions: ["jpg", "png", "pdf", "mp4"]
        enable-rate-limit: true
        max-requests-per-second: 50
      
      tenant2:
        name: "租户2"
        secret-key: "tenant2-secret-key"
        base-path: "./files/tenant2"
        expire-seconds: 3600
        forbidden-extensions: ["exe", "bat", "sh"]
        enable-rate-limit: false

# Spring Boot配置
spring:
  application:
    name: sxr-file-signature
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# 日志配置
logging:
  level:
    com.sxr: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
